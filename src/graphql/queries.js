/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const accuracy = /* GraphQL */ `
  query Accuracy(
    $owner: String
    $clubCategory: String
    $start: Int
    $end: Int
  ) {
    accuracy(
      owner: $owner
      clubCategory: $clubCategory
      start: $start
      end: $end
    ) {
      range
      count
      __typename
    }
  }
`;
export const countByClub = /* GraphQL */ `
  query CountByClub($owner: String, $start: Int, $end: Int) {
    countByClub(owner: $owner, start: $start, end: $end) {
      clubId
      count
      __typename
    }
  }
`;
export const sessionCount = /* GraphQL */ `
  query SessionCount(
    $owner: String
    $resolution: DateResolution
    $start: Int
    $end: Int
  ) {
    sessionCount(
      owner: $owner
      resolution: $resolution
      start: $start
      end: $end
    ) {
      start
      end
      count
      __typename
    }
  }
`;
export const shotCount = /* GraphQL */ `
  query ShotCount(
    $owner: String
    $resolution: DateResolution
    $start: Int
    $end: Int
  ) {
    shotCount(
      owner: $owner
      resolution: $resolution
      start: $start
      end: $end
    ) {
      start
      end
      count
      __typename
    }
  }
`;
export const averageByClub = /* GraphQL */ `
  query AverageByClub(
    $owner: String
    $value: ValueType
    $start: Int
    $end: Int
  ) {
    averageByClub(owner: $owner, value: $value, start: $start, end: $end) {
      clubId
      average
      __typename
    }
  }
`;
export const getShadow = /* GraphQL */ `
  query GetShadow($deviceId: String) {
    getShadow(deviceId: $deviceId) {
      regUser
      prodRegDate
      __typename
    }
  }
`;
export const userExists = /* GraphQL */ `
  query UserExists($email: String) {
    userExists(email: $email) {
      status
      __typename
    }
  }
`;
export const sessionsByTeam = /* GraphQL */ `
  query SessionsByTeam($teamId: String, $nextToken: String) {
    sessionsByTeam(teamId: $teamId, nextToken: $nextToken) {
      nextToken
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      __typename
    }
  }
`;
export const resultsByTeamSession = /* GraphQL */ `
  query ResultsByTeamSession($sessionId: String, $nextToken: String) {
    resultsByTeamSession(sessionId: $sessionId, nextToken: $nextToken) {
      nextToken
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      __typename
    }
  }
`;
export const sessionsByUserId = /* GraphQL */ `
  query SessionsByUserId($userId: String, $nextToken: String) {
    sessionsByUserId(userId: $userId, nextToken: $nextToken) {
      nextToken
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      __typename
    }
  }
`;
export const getLmTeamInviteCodes = /* GraphQL */ `
  query GetLmTeamInviteCodes($id: ID!) {
    getLmTeamInviteCodes(id: $id) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmTeamInviteCodes = /* GraphQL */ `
  query ListLmTeamInviteCodes(
    $id: ID
    $filter: ModelLmTeamInviteCodesFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmTeamInviteCodes(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        link
        owner
        sport
        type
        email
        lmPlayerName
        lmTeamId
        expired_at
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmTeamInviteCodes = /* GraphQL */ `
  query SyncLmTeamInviteCodes(
    $filter: ModelLmTeamInviteCodesFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmTeamInviteCodes(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        link
        owner
        sport
        type
        email
        lmPlayerName
        lmTeamId
        expired_at
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamInviteCodesByTeam = /* GraphQL */ `
  query TeamInviteCodesByTeam(
    $lmTeamId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamInviteCodesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamInviteCodesByTeam(
      lmTeamId: $lmTeamId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        link
        owner
        sport
        type
        email
        lmPlayerName
        lmTeamId
        expired_at
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmTeamPlayers = /* GraphQL */ `
  query GetLmTeamPlayers($id: ID!) {
    getLmTeamPlayers(id: $id) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmTeamPlayers = /* GraphQL */ `
  query ListLmTeamPlayers(
    $id: ID
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmTeamPlayers(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmTeamPlayers = /* GraphQL */ `
  query SyncLmTeamPlayers(
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmTeamPlayers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamPlayersByUser = /* GraphQL */ `
  query TeamPlayersByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamPlayersByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamPlayersByEmail = /* GraphQL */ `
  query TeamPlayersByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamPlayersByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamPlayersByTeam = /* GraphQL */ `
  query TeamPlayersByTeam(
    $lmTeamId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamPlayersByTeam(
      lmTeamId: $lmTeamId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamPlayersByTeamAndJoinedAt = /* GraphQL */ `
  query TeamPlayersByTeamAndJoinedAt(
    $lmTeamId: ID!
    $joined_at: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamPlayersByTeamAndJoinedAt(
      lmTeamId: $lmTeamId
      joined_at: $joined_at
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamPlayersByPlayer = /* GraphQL */ `
  query TeamPlayersByPlayer(
    $lmPlayerId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamPlayersByPlayer(
      lmPlayerId: $lmPlayerId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmTeamCoaches = /* GraphQL */ `
  query GetLmTeamCoaches($id: ID!) {
    getLmTeamCoaches(id: $id) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmTeamCoaches = /* GraphQL */ `
  query ListLmTeamCoaches(
    $id: ID
    $filter: ModelLmTeamCoachesFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmTeamCoaches(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        owner
        lmTeamId
        lmCoachId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmTeamCoaches = /* GraphQL */ `
  query SyncLmTeamCoaches(
    $filter: ModelLmTeamCoachesFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmTeamCoaches(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        owner
        lmTeamId
        lmCoachId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamCoachesByUser = /* GraphQL */ `
  query TeamCoachesByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamCoachesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamCoachesByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmTeamId
        lmCoachId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamCoachesByTeam = /* GraphQL */ `
  query TeamCoachesByTeam(
    $lmTeamId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamCoachesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamCoachesByTeam(
      lmTeamId: $lmTeamId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmTeamId
        lmCoachId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamCoachesByCoach = /* GraphQL */ `
  query TeamCoachesByCoach(
    $lmCoachId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamCoachesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamCoachesByCoach(
      lmCoachId: $lmCoachId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmTeamId
        lmCoachId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmTeam = /* GraphQL */ `
  query GetLmTeam($id: ID!) {
    getLmTeam(id: $id) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmTeams = /* GraphQL */ `
  query ListLmTeams(
    $id: ID
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmTeams(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmTeams = /* GraphQL */ `
  query SyncLmTeams(
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmTeams(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamsByOwner = /* GraphQL */ `
  query TeamsByOwner(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamsByOwner(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmCoachPlayers = /* GraphQL */ `
  query GetLmCoachPlayers($id: ID!) {
    getLmCoachPlayers(id: $id) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmCoachPlayers = /* GraphQL */ `
  query ListLmCoachPlayers(
    $id: ID
    $filter: ModelLmCoachPlayersFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmCoachPlayers(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmCoachPlayers = /* GraphQL */ `
  query SyncLmCoachPlayers(
    $filter: ModelLmCoachPlayersFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmCoachPlayers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const coachPlayersByCoach = /* GraphQL */ `
  query CoachPlayersByCoach(
    $lmCoachId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmCoachPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    coachPlayersByCoach(
      lmCoachId: $lmCoachId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const coachPlayersLmPlayerEmail = /* GraphQL */ `
  query CoachPlayersLmPlayerEmail(
    $lmPlayerEmail: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmCoachPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    coachPlayersLmPlayerEmail(
      lmPlayerEmail: $lmPlayerEmail
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const coachPlayersByTeamAssigned = /* GraphQL */ `
  query CoachPlayersByTeamAssigned(
    $teamAssigned: LmCoachPlayersTeamAssigned!
    $sortDirection: ModelSortDirection
    $filter: ModelLmCoachPlayersFilterInput
    $limit: Int
    $nextToken: String
  ) {
    coachPlayersByTeamAssigned(
      teamAssigned: $teamAssigned
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmUser = /* GraphQL */ `
  query GetLmUser($id: ID!) {
    getLmUser(id: $id) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const listLmUsers = /* GraphQL */ `
  query ListLmUsers(
    $id: ID
    $filter: ModelLmUserFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmUsers(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmUsers = /* GraphQL */ `
  query SyncLmUsers(
    $filter: ModelLmUserFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmUsers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmProfile = /* GraphQL */ `
  query GetLmProfile($id: ID!) {
    getLmProfile(id: $id) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const listLmProfiles = /* GraphQL */ `
  query ListLmProfiles(
    $id: ID
    $filter: ModelLmProfileFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmProfiles(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmProfiles = /* GraphQL */ `
  query SyncLmProfiles(
    $filter: ModelLmProfileFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmProfiles(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const profileByOwner = /* GraphQL */ `
  query ProfileByOwner(
    $userId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    profileByOwner(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmFeature = /* GraphQL */ `
  query GetLmFeature($id: ID!) {
    getLmFeature(id: $id) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmFeatures = /* GraphQL */ `
  query ListLmFeatures(
    $id: ID
    $filter: ModelLmFeatureFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmFeatures(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmProfileId
        owner
        featureName
        enabled
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmFeatures = /* GraphQL */ `
  query SyncLmFeatures(
    $filter: ModelLmFeatureFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmFeatures(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmProfileId
        owner
        featureName
        enabled
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const featuresByProfile = /* GraphQL */ `
  query FeaturesByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmFeatureFilterInput
    $limit: Int
    $nextToken: String
  ) {
    featuresByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        featureName
        enabled
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const featuresByUser = /* GraphQL */ `
  query FeaturesByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmFeatureFilterInput
    $limit: Int
    $nextToken: String
  ) {
    featuresByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        featureName
        enabled
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmDevice = /* GraphQL */ `
  query GetLmDevice($id: ID!) {
    getLmDevice(id: $id) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmDevices = /* GraphQL */ `
  query ListLmDevices(
    $id: ID
    $filter: ModelLmDeviceFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmDevices(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        advertisementName
        connectID
        configuredWifiSSID
        serialNumber
        modelNumber
        firmwareVersion
        autoConnect
        registered
        registeredUser
        registrationDate
        registrationReminder
        registrationReminderTime
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmDevices = /* GraphQL */ `
  query SyncLmDevices(
    $filter: ModelLmDeviceFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmDevices(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        advertisementName
        connectID
        configuredWifiSSID
        serialNumber
        modelNumber
        firmwareVersion
        autoConnect
        registered
        registeredUser
        registrationDate
        registrationReminder
        registrationReminderTime
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const devicesByProfile = /* GraphQL */ `
  query DevicesByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDeviceFilterInput
    $limit: Int
    $nextToken: String
  ) {
    devicesByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        advertisementName
        connectID
        configuredWifiSSID
        serialNumber
        modelNumber
        firmwareVersion
        autoConnect
        registered
        registeredUser
        registrationDate
        registrationReminder
        registrationReminderTime
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const devicesByUser = /* GraphQL */ `
  query DevicesByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDeviceFilterInput
    $limit: Int
    $nextToken: String
  ) {
    devicesByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        advertisementName
        connectID
        configuredWifiSSID
        serialNumber
        modelNumber
        firmwareVersion
        autoConnect
        registered
        registeredUser
        registrationDate
        registrationReminder
        registrationReminderTime
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmUserStats = /* GraphQL */ `
  query GetLmUserStats($id: ID!) {
    getLmUserStats(id: $id) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmUserStats = /* GraphQL */ `
  query ListLmUserStats(
    $id: ID
    $filter: ModelLmUserStatsFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmUserStats(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmProfileId
        owner
        sessionCount
        shotCount
        clubCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmUserStats = /* GraphQL */ `
  query SyncLmUserStats(
    $filter: ModelLmUserStatsFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmUserStats(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmProfileId
        owner
        sessionCount
        shotCount
        clubCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const statsByProfile = /* GraphQL */ `
  query StatsByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmUserStatsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    statsByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        sessionCount
        shotCount
        clubCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const statsByOwner = /* GraphQL */ `
  query StatsByOwner(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmUserStatsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    statsByOwner(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        sessionCount
        shotCount
        clubCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmSession = /* GraphQL */ `
  query GetLmSession($id: ID!) {
    getLmSession(id: $id) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmSessions = /* GraphQL */ `
  query ListLmSessions(
    $id: ID
    $filter: ModelLmSessionFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmSessions(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        startTimestamp
        endTimestamp
        duration
        name
        city
        state
        country
        address
        courseName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalized
        sessionQuality
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmSessions = /* GraphQL */ `
  query SyncLmSessions(
    $filter: ModelLmSessionFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmSessions(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        startTimestamp
        endTimestamp
        duration
        name
        city
        state
        country
        address
        courseName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalized
        sessionQuality
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const sessionsByProfile = /* GraphQL */ `
  query SessionsByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    sessionsByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        startTimestamp
        endTimestamp
        duration
        name
        city
        state
        country
        address
        courseName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalized
        sessionQuality
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const sessionsByUser = /* GraphQL */ `
  query SessionsByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    sessionsByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        startTimestamp
        endTimestamp
        duration
        name
        city
        state
        country
        address
        courseName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalized
        sessionQuality
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const sessionsByUserInRange = /* GraphQL */ `
  query SessionsByUserInRange(
    $owner: String!
    $startTimestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    sessionsByUserInRange(
      owner: $owner
      startTimestamp: $startTimestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        deviceID
        startTimestamp
        endTimestamp
        duration
        name
        city
        state
        country
        address
        courseName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalized
        sessionQuality
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmShot = /* GraphQL */ `
  query GetLmShot($id: ID!) {
    getLmShot(id: $id) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmShots = /* GraphQL */ `
  query ListLmShots(
    $id: ID
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmShots(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmShots = /* GraphQL */ `
  query SyncLmShots(
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmShots(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsBySession = /* GraphQL */ `
  query ShotsBySession(
    $lmSessionId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsBySession(
      lmSessionId: $lmSessionId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsBySessionInRange = /* GraphQL */ `
  query ShotsBySessionInRange(
    $lmSessionId: ID!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsBySessionInRange(
      lmSessionId: $lmSessionId
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByDrillTarget = /* GraphQL */ `
  query ShotsByDrillTarget(
    $lmDrillTargetId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByDrillTarget(
      lmDrillTargetId: $lmDrillTargetId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByDrillTargetInRange = /* GraphQL */ `
  query ShotsByDrillTargetInRange(
    $lmDrillTargetId: ID!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByDrillTargetInRange(
      lmDrillTargetId: $lmDrillTargetId
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByClub = /* GraphQL */ `
  query ShotsByClub(
    $clubId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByClub(
      clubId: $clubId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByClubInRange = /* GraphQL */ `
  query ShotsByClubInRange(
    $clubId: ID!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByClubInRange(
      clubId: $clubId
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByUser = /* GraphQL */ `
  query ShotsByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByUserInRange = /* GraphQL */ `
  query ShotsByUserInRange(
    $owner: String!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByUserInRange(
      owner: $owner
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shotsByClubCategoryInRange = /* GraphQL */ `
  query ShotsByClubCategoryInRange(
    $clubCategory: String!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmShotFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shotsByClubCategoryInRange(
      clubCategory: $clubCategory
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        lmDrillTargetId
        clubId
        owner
        clubCategory
        pointId
        timestamp
        isFavorite
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        targetDistance
        distanceToPin
        impactKey
        videoKey
        pointCloudKey
        protobufKey
        clubSpeedValid
        ballSpeedValid
        smashFactorValid
        attackAngleValid
        clubPathValid
        launchAngleValid
        horizontalLaunchAngleValid
        faceAngleValid
        spinRateValid
        spinAxisValid
        carryDistanceValid
        totalDistanceValid
        sideValid
        sideTotalValid
        apexValid
        descentAngleValid
        dynamicLoftValid
        xFit
        yFit
        zFit
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmClub = /* GraphQL */ `
  query GetLmClub($id: ID!) {
    getLmClub(id: $id) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmClubs = /* GraphQL */ `
  query ListLmClubs(
    $id: ID
    $filter: ModelLmClubFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmClubs(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmClubs = /* GraphQL */ `
  query SyncLmClubs(
    $filter: ModelLmClubFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmClubs(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const clubsByProfile = /* GraphQL */ `
  query ClubsByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmClubFilterInput
    $limit: Int
    $nextToken: String
  ) {
    clubsByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const clubsByOwner = /* GraphQL */ `
  query ClubsByOwner(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmClubFilterInput
    $limit: Int
    $nextToken: String
  ) {
    clubsByOwner(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmDrillTemplate = /* GraphQL */ `
  query GetLmDrillTemplate($id: ID!) {
    getLmDrillTemplate(id: $id) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmDrillTemplates = /* GraphQL */ `
  query ListLmDrillTemplates(
    $id: ID
    $filter: ModelLmDrillTemplateFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmDrillTemplates(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        type
        name
        ownerName
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmDrillTemplates = /* GraphQL */ `
  query SyncLmDrillTemplates(
    $filter: ModelLmDrillTemplateFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmDrillTemplates(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        type
        name
        ownerName
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillTemplatesByType = /* GraphQL */ `
  query DrillTemplatesByType(
    $type: LmDrillTargetType!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillTemplateFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillTemplatesByType(
      type: $type
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        type
        name
        ownerName
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillTemplatesByName = /* GraphQL */ `
  query DrillTemplatesByName(
    $name: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillTemplateFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillTemplatesByName(
      name: $name
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        type
        name
        ownerName
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillTemplatesByNameAndOwner = /* GraphQL */ `
  query DrillTemplatesByNameAndOwner(
    $name: String!
    $ownerName: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillTemplateFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillTemplatesByNameAndOwner(
      name: $name
      ownerName: $ownerName
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        type
        name
        ownerName
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmDrill = /* GraphQL */ `
  query GetLmDrill($id: ID!) {
    getLmDrill(id: $id) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmDrills = /* GraphQL */ `
  query ListLmDrills(
    $id: ID
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmDrills(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmDrills = /* GraphQL */ `
  query SyncLmDrills(
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmDrills(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillsByUser = /* GraphQL */ `
  query DrillsByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillsByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillsByProfile = /* GraphQL */ `
  query DrillsByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillsByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillsByTempate = /* GraphQL */ `
  query DrillsByTempate(
    $templateId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillsByTempate(
      templateId: $templateId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const drillsByType = /* GraphQL */ `
  query DrillsByType(
    $type: LmDrillTargetType!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDrillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    drillsByType(
      type: $type
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        lmProfileId
        templateId
        type
        name
        logoUrl
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmSessionStats = /* GraphQL */ `
  query GetLmSessionStats($id: ID!) {
    getLmSessionStats(id: $id) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmSessionStats = /* GraphQL */ `
  query ListLmSessionStats(
    $id: ID
    $filter: ModelLmSessionStatsFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmSessionStats(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        lmSessionId
        owner
        shotCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmSessionStats = /* GraphQL */ `
  query SyncLmSessionStats(
    $filter: ModelLmSessionStatsFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmSessionStats(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        lmSessionId
        owner
        shotCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const statsBySession = /* GraphQL */ `
  query StatsBySession(
    $lmSessionId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmSessionStatsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    statsBySession(
      lmSessionId: $lmSessionId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        owner
        shotCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const sessionStatsByOwner = /* GraphQL */ `
  query SessionStatsByOwner(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmSessionStatsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    sessionStatsByOwner(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        lmSessionId
        owner
        shotCount
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmShareSession = /* GraphQL */ `
  query GetLmShareSession($shareUrl: ID!) {
    getLmShareSession(shareUrl: $shareUrl) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmShareSessions = /* GraphQL */ `
  query ListLmShareSessions(
    $shareUrl: ID
    $filter: ModelLmShareSessionFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmShareSessions(
      shareUrl: $shareUrl
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        shareUrl
        lmSessionId
        owner
        shareVideo
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmShareSessions = /* GraphQL */ `
  query SyncLmShareSessions(
    $filter: ModelLmShareSessionFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmShareSessions(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        shareUrl
        lmSessionId
        owner
        shareVideo
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shareBySession = /* GraphQL */ `
  query ShareBySession(
    $lmSessionId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShareSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shareBySession(
      lmSessionId: $lmSessionId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        shareUrl
        lmSessionId
        owner
        shareVideo
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const shareByUser = /* GraphQL */ `
  query ShareByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmShareSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shareByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        shareUrl
        lmSessionId
        owner
        shareVideo
        expiresAt
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmDataResults = /* GraphQL */ `
  query GetLmDataResults($id: ID!) {
    getLmDataResults(id: $id) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmDataResults = /* GraphQL */ `
  query ListLmDataResults(
    $id: ID
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmDataResults(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmDataResults = /* GraphQL */ `
  query SyncLmDataResults(
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmDataResults(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataResultsBySession = /* GraphQL */ `
  query DataResultsBySession(
    $lmSessionId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataResultsBySession(
      lmSessionId: $lmSessionId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataResultsBySessionInRange = /* GraphQL */ `
  query DataResultsBySessionInRange(
    $lmSessionId: ID!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataResultsBySessionInRange(
      lmSessionId: $lmSessionId
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataResultsByUser = /* GraphQL */ `
  query DataResultsByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataResultsByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataResultsByUserInRange = /* GraphQL */ `
  query DataResultsByUserInRange(
    $owner: String!
    $timestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataResultsByUserInRange(
      owner: $owner
      timestamp: $timestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataResultsBySport = /* GraphQL */ `
  query DataResultsBySport(
    $sport: LmSportType!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataResultsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataResultsBySport(
      sport: $sport
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        resultId
        sequenceNumber
        lmSessionId
        owner
        sport
        readers
        timestamp
        isFavorite
        videoKey
        pointCloudKey
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmDataSession = /* GraphQL */ `
  query GetLmDataSession($id: ID!) {
    getLmDataSession(id: $id) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmDataSessions = /* GraphQL */ `
  query ListLmDataSessions(
    $id: ID
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmDataSessions(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmDataSessions = /* GraphQL */ `
  query SyncLmDataSessions(
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmDataSessions(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataSessionsByProfile = /* GraphQL */ `
  query DataSessionsByProfile(
    $lmProfileId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataSessionsByProfile(
      lmProfileId: $lmProfileId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataSessionsByTeam = /* GraphQL */ `
  query DataSessionsByTeam(
    $lmTeamId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataSessionsByTeam(
      lmTeamId: $lmTeamId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const sportSessionsByUser = /* GraphQL */ `
  query SportSessionsByUser(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    sportSessionsByUser(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataSessionsByUserInRange = /* GraphQL */ `
  query DataSessionsByUserInRange(
    $owner: String!
    $startTimestamp: ModelIntKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataSessionsByUserInRange(
      owner: $owner
      startTimestamp: $startTimestamp
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const dataSessionBySport = /* GraphQL */ `
  query DataSessionBySport(
    $sport: LmSportType!
    $sortDirection: ModelSortDirection
    $filter: ModelLmDataSessionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    dataSessionBySport(
      sport: $sport
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        deviceID
        lmProfileId
        lmTeamId
        owner
        sport
        startTimestamp
        endTimestamp
        duration
        readers
        name
        details
        city
        state
        country
        address
        locationName
        elevation
        latitude
        longitude
        temperature
        humidity
        location
        playMode
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
