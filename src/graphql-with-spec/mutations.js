/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const shareSession = /* GraphQL */ `
  mutation ShareSession($sessionId: String) {
    shareSession(sessionId: $sessionId) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const writeShadow = /* GraphQL */ `
  mutation WriteShadow($deviceId: String, $regUser: String) {
    writeShadow(deviceId: $deviceId, regUser: $regUser) {
      regUser
      prodRegDate
      __typename
    }
  }
`;
export const clearUserAccount = /* GraphQL */ `
  mutation ClearUserAccount($userId: String) {
    clearUserAccount(userId: $userId) {
      status
      __typename
    }
  }
`;
export const updateFeature = /* GraphQL */ `
  mutation UpdateFeature($featureId: String, $enabled: Boolean) {
    updateFeature(featureId: $featureId, enabled: $enabled) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmTeamInviteCodes = /* GraphQL */ `
  mutation CreateLmTeamInviteCodes(
    $input: CreateLmTeamInviteCodesInput!
    $condition: ModelLmTeamInviteCodesConditionInput
  ) {
    createLmTeamInviteCodes(input: $input, condition: $condition) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmTeamInviteCodes = /* GraphQL */ `
  mutation UpdateLmTeamInviteCodes(
    $input: UpdateLmTeamInviteCodesInput!
    $condition: ModelLmTeamInviteCodesConditionInput
  ) {
    updateLmTeamInviteCodes(input: $input, condition: $condition) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmTeamInviteCodes = /* GraphQL */ `
  mutation DeleteLmTeamInviteCodes(
    $input: DeleteLmTeamInviteCodesInput!
    $condition: ModelLmTeamInviteCodesConditionInput
  ) {
    deleteLmTeamInviteCodes(input: $input, condition: $condition) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmTeamPlayers = /* GraphQL */ `
  mutation CreateLmTeamPlayers(
    $input: CreateLmTeamPlayersInput!
    $condition: ModelLmTeamPlayersConditionInput
  ) {
    createLmTeamPlayers(input: $input, condition: $condition) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmTeamPlayers = /* GraphQL */ `
  mutation UpdateLmTeamPlayers(
    $input: UpdateLmTeamPlayersInput!
    $condition: ModelLmTeamPlayersConditionInput
  ) {
    updateLmTeamPlayers(input: $input, condition: $condition) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmTeamPlayers = /* GraphQL */ `
  mutation DeleteLmTeamPlayers(
    $input: DeleteLmTeamPlayersInput!
    $condition: ModelLmTeamPlayersConditionInput
  ) {
    deleteLmTeamPlayers(input: $input, condition: $condition) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmTeamCoaches = /* GraphQL */ `
  mutation CreateLmTeamCoaches(
    $input: CreateLmTeamCoachesInput!
    $condition: ModelLmTeamCoachesConditionInput
  ) {
    createLmTeamCoaches(input: $input, condition: $condition) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmTeamCoaches = /* GraphQL */ `
  mutation UpdateLmTeamCoaches(
    $input: UpdateLmTeamCoachesInput!
    $condition: ModelLmTeamCoachesConditionInput
  ) {
    updateLmTeamCoaches(input: $input, condition: $condition) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmTeamCoaches = /* GraphQL */ `
  mutation DeleteLmTeamCoaches(
    $input: DeleteLmTeamCoachesInput!
    $condition: ModelLmTeamCoachesConditionInput
  ) {
    deleteLmTeamCoaches(input: $input, condition: $condition) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmTeam = /* GraphQL */ `
  mutation CreateLmTeam(
    $input: CreateLmTeamInput!
    $condition: ModelLmTeamConditionInput
  ) {
    createLmTeam(input: $input, condition: $condition) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmTeam = /* GraphQL */ `
  mutation UpdateLmTeam(
    $input: UpdateLmTeamInput!
    $condition: ModelLmTeamConditionInput
  ) {
    updateLmTeam(input: $input, condition: $condition) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmTeam = /* GraphQL */ `
  mutation DeleteLmTeam(
    $input: DeleteLmTeamInput!
    $condition: ModelLmTeamConditionInput
  ) {
    deleteLmTeam(input: $input, condition: $condition) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmCoachPlayers = /* GraphQL */ `
  mutation CreateLmCoachPlayers(
    $input: CreateLmCoachPlayersInput!
    $condition: ModelLmCoachPlayersConditionInput
  ) {
    createLmCoachPlayers(input: $input, condition: $condition) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmCoachPlayers = /* GraphQL */ `
  mutation UpdateLmCoachPlayers(
    $input: UpdateLmCoachPlayersInput!
    $condition: ModelLmCoachPlayersConditionInput
  ) {
    updateLmCoachPlayers(input: $input, condition: $condition) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmCoachPlayers = /* GraphQL */ `
  mutation DeleteLmCoachPlayers(
    $input: DeleteLmCoachPlayersInput!
    $condition: ModelLmCoachPlayersConditionInput
  ) {
    deleteLmCoachPlayers(input: $input, condition: $condition) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmUser = /* GraphQL */ `
  mutation CreateLmUser(
    $input: CreateLmUserInput!
    $condition: ModelLmUserConditionInput
  ) {
    createLmUser(input: $input, condition: $condition) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const updateLmUser = /* GraphQL */ `
  mutation UpdateLmUser(
    $input: UpdateLmUserInput!
    $condition: ModelLmUserConditionInput
  ) {
    updateLmUser(input: $input, condition: $condition) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const deleteLmUser = /* GraphQL */ `
  mutation DeleteLmUser(
    $input: DeleteLmUserInput!
    $condition: ModelLmUserConditionInput
  ) {
    deleteLmUser(input: $input, condition: $condition) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const createLmProfile = /* GraphQL */ `
  mutation CreateLmProfile(
    $input: CreateLmProfileInput!
    $condition: ModelLmProfileConditionInput
  ) {
    createLmProfile(input: $input, condition: $condition) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const updateLmProfile = /* GraphQL */ `
  mutation UpdateLmProfile(
    $input: UpdateLmProfileInput!
    $condition: ModelLmProfileConditionInput
  ) {
    updateLmProfile(input: $input, condition: $condition) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const deleteLmProfile = /* GraphQL */ `
  mutation DeleteLmProfile(
    $input: DeleteLmProfileInput!
    $condition: ModelLmProfileConditionInput
  ) {
    deleteLmProfile(input: $input, condition: $condition) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const createLmFeature = /* GraphQL */ `
  mutation CreateLmFeature(
    $input: CreateLmFeatureInput!
    $condition: ModelLmFeatureConditionInput
  ) {
    createLmFeature(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmFeature = /* GraphQL */ `
  mutation UpdateLmFeature(
    $input: UpdateLmFeatureInput!
    $condition: ModelLmFeatureConditionInput
  ) {
    updateLmFeature(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmFeature = /* GraphQL */ `
  mutation DeleteLmFeature(
    $input: DeleteLmFeatureInput!
    $condition: ModelLmFeatureConditionInput
  ) {
    deleteLmFeature(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmDevice = /* GraphQL */ `
  mutation CreateLmDevice(
    $input: CreateLmDeviceInput!
    $condition: ModelLmDeviceConditionInput
  ) {
    createLmDevice(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmDevice = /* GraphQL */ `
  mutation UpdateLmDevice(
    $input: UpdateLmDeviceInput!
    $condition: ModelLmDeviceConditionInput
  ) {
    updateLmDevice(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmDevice = /* GraphQL */ `
  mutation DeleteLmDevice(
    $input: DeleteLmDeviceInput!
    $condition: ModelLmDeviceConditionInput
  ) {
    deleteLmDevice(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmUserStats = /* GraphQL */ `
  mutation CreateLmUserStats(
    $input: CreateLmUserStatsInput!
    $condition: ModelLmUserStatsConditionInput
  ) {
    createLmUserStats(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmUserStats = /* GraphQL */ `
  mutation UpdateLmUserStats(
    $input: UpdateLmUserStatsInput!
    $condition: ModelLmUserStatsConditionInput
  ) {
    updateLmUserStats(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmUserStats = /* GraphQL */ `
  mutation DeleteLmUserStats(
    $input: DeleteLmUserStatsInput!
    $condition: ModelLmUserStatsConditionInput
  ) {
    deleteLmUserStats(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmSession = /* GraphQL */ `
  mutation CreateLmSession(
    $input: CreateLmSessionInput!
    $condition: ModelLmSessionConditionInput
  ) {
    createLmSession(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmSession = /* GraphQL */ `
  mutation UpdateLmSession(
    $input: UpdateLmSessionInput!
    $condition: ModelLmSessionConditionInput
  ) {
    updateLmSession(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmSession = /* GraphQL */ `
  mutation DeleteLmSession(
    $input: DeleteLmSessionInput!
    $condition: ModelLmSessionConditionInput
  ) {
    deleteLmSession(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmShot = /* GraphQL */ `
  mutation CreateLmShot(
    $input: CreateLmShotInput!
    $condition: ModelLmShotConditionInput
  ) {
    createLmShot(input: $input, condition: $condition) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmShot = /* GraphQL */ `
  mutation UpdateLmShot(
    $input: UpdateLmShotInput!
    $condition: ModelLmShotConditionInput
  ) {
    updateLmShot(input: $input, condition: $condition) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmShot = /* GraphQL */ `
  mutation DeleteLmShot(
    $input: DeleteLmShotInput!
    $condition: ModelLmShotConditionInput
  ) {
    deleteLmShot(input: $input, condition: $condition) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmClub = /* GraphQL */ `
  mutation CreateLmClub(
    $input: CreateLmClubInput!
    $condition: ModelLmClubConditionInput
  ) {
    createLmClub(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmClub = /* GraphQL */ `
  mutation UpdateLmClub(
    $input: UpdateLmClubInput!
    $condition: ModelLmClubConditionInput
  ) {
    updateLmClub(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmClub = /* GraphQL */ `
  mutation DeleteLmClub(
    $input: DeleteLmClubInput!
    $condition: ModelLmClubConditionInput
  ) {
    deleteLmClub(input: $input, condition: $condition) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmDrillTemplate = /* GraphQL */ `
  mutation CreateLmDrillTemplate(
    $input: CreateLmDrillTemplateInput!
    $condition: ModelLmDrillTemplateConditionInput
  ) {
    createLmDrillTemplate(input: $input, condition: $condition) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmDrillTemplate = /* GraphQL */ `
  mutation UpdateLmDrillTemplate(
    $input: UpdateLmDrillTemplateInput!
    $condition: ModelLmDrillTemplateConditionInput
  ) {
    updateLmDrillTemplate(input: $input, condition: $condition) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmDrillTemplate = /* GraphQL */ `
  mutation DeleteLmDrillTemplate(
    $input: DeleteLmDrillTemplateInput!
    $condition: ModelLmDrillTemplateConditionInput
  ) {
    deleteLmDrillTemplate(input: $input, condition: $condition) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmDrill = /* GraphQL */ `
  mutation CreateLmDrill(
    $input: CreateLmDrillInput!
    $condition: ModelLmDrillConditionInput
  ) {
    createLmDrill(input: $input, condition: $condition) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmDrill = /* GraphQL */ `
  mutation UpdateLmDrill(
    $input: UpdateLmDrillInput!
    $condition: ModelLmDrillConditionInput
  ) {
    updateLmDrill(input: $input, condition: $condition) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmDrill = /* GraphQL */ `
  mutation DeleteLmDrill(
    $input: DeleteLmDrillInput!
    $condition: ModelLmDrillConditionInput
  ) {
    deleteLmDrill(input: $input, condition: $condition) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmSessionStats = /* GraphQL */ `
  mutation CreateLmSessionStats(
    $input: CreateLmSessionStatsInput!
    $condition: ModelLmSessionStatsConditionInput
  ) {
    createLmSessionStats(input: $input, condition: $condition) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmSessionStats = /* GraphQL */ `
  mutation UpdateLmSessionStats(
    $input: UpdateLmSessionStatsInput!
    $condition: ModelLmSessionStatsConditionInput
  ) {
    updateLmSessionStats(input: $input, condition: $condition) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmSessionStats = /* GraphQL */ `
  mutation DeleteLmSessionStats(
    $input: DeleteLmSessionStatsInput!
    $condition: ModelLmSessionStatsConditionInput
  ) {
    deleteLmSessionStats(input: $input, condition: $condition) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmShareSession = /* GraphQL */ `
  mutation CreateLmShareSession(
    $input: CreateLmShareSessionInput!
    $condition: ModelLmShareSessionConditionInput
  ) {
    createLmShareSession(input: $input, condition: $condition) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmShareSession = /* GraphQL */ `
  mutation UpdateLmShareSession(
    $input: UpdateLmShareSessionInput!
    $condition: ModelLmShareSessionConditionInput
  ) {
    updateLmShareSession(input: $input, condition: $condition) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmShareSession = /* GraphQL */ `
  mutation DeleteLmShareSession(
    $input: DeleteLmShareSessionInput!
    $condition: ModelLmShareSessionConditionInput
  ) {
    deleteLmShareSession(input: $input, condition: $condition) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmDataResults = /* GraphQL */ `
  mutation CreateLmDataResults(
    $input: CreateLmDataResultsInput!
    $condition: ModelLmDataResultsConditionInput
  ) {
    createLmDataResults(input: $input, condition: $condition) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmDataResults = /* GraphQL */ `
  mutation UpdateLmDataResults(
    $input: UpdateLmDataResultsInput!
    $condition: ModelLmDataResultsConditionInput
  ) {
    updateLmDataResults(input: $input, condition: $condition) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmDataResults = /* GraphQL */ `
  mutation DeleteLmDataResults(
    $input: DeleteLmDataResultsInput!
    $condition: ModelLmDataResultsConditionInput
  ) {
    deleteLmDataResults(input: $input, condition: $condition) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const createLmDataSession = /* GraphQL */ `
  mutation CreateLmDataSession(
    $input: CreateLmDataSessionInput!
    $condition: ModelLmDataSessionConditionInput
  ) {
    createLmDataSession(input: $input, condition: $condition) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const updateLmDataSession = /* GraphQL */ `
  mutation UpdateLmDataSession(
    $input: UpdateLmDataSessionInput!
    $condition: ModelLmDataSessionConditionInput
  ) {
    updateLmDataSession(input: $input, condition: $condition) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const deleteLmDataSession = /* GraphQL */ `
  mutation DeleteLmDataSession(
    $input: DeleteLmDataSessionInput!
    $condition: ModelLmDataSessionConditionInput
  ) {
    deleteLmDataSession(input: $input, condition: $condition) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const clearShadow = /* GraphQL */ `
  mutation ClearShadow($deviceId: String, $regUser: String) {
    clearShadow(deviceId: $deviceId, regUser: $regUser) {
      regUser
      prodRegDate
      __typename
    }
  }
`;
export const chooseUserRole = /* GraphQL */ `
  mutation ChooseUserRole($role: LmUserRole!) {
    chooseUserRole(role: $role) {
      status
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const createLmTeamInviteLink = /* GraphQL */ `
  mutation CreateLmTeamInviteLink($lmTeamId: String!) {
    createLmTeamInviteLink(lmTeamId: $lmTeamId) {
      status
      link
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const acceptTeamInvite = /* GraphQL */ `
  mutation AcceptTeamInvite($inviteCode: String!) {
    acceptTeamInvite(inviteCode: $inviteCode) {
      status
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const addPlayerToTeam = /* GraphQL */ `
  mutation AddPlayerToTeam(
    $lmTeamId: String
    $email: String
    $userId: String
    $fullName: String
    $playerType: LmTeamPlayerType
  ) {
    addPlayerToTeam(
      lmTeamId: $lmTeamId
      email: $email
      userId: $userId
      fullName: $fullName
      playerType: $playerType
    ) {
      status
      isExist
      teamPlayer {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      coachPlayer {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const updateLmUserCustom = /* GraphQL */ `
  mutation UpdateLmUserCustom($input: LmUserInfoUpdateInput!) {
    updateLmUserCustom(input: $input) {
      status
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const deleteLmTeamPlayersCustom = /* GraphQL */ `
  mutation DeleteLmTeamPlayersCustom($lmTeamPlayerId: ID!) {
    deleteLmTeamPlayersCustom(lmTeamPlayerId: $lmTeamPlayerId) {
      status
      teamAssigned
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const deleteLmTeamCustom = /* GraphQL */ `
  mutation DeleteLmTeamCustom($lmTeamId: ID!) {
    deleteLmTeamCustom(lmTeamId: $lmTeamId) {
      status
      teamDeleted {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      teamPlayersDeleted {
        id
        owner
        readers
        type
        status
        email
        joined_at
        lmTeamId
        lmPlayerId
        lmPlayerName
        lineupOrderBatting
        lineupOrderPitching
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      coachPlayersNotAssigned {
        id
        lmCoachId
        owner
        lmPlayerId
        lmPlayerName
        status
        lmPlayerEmail
        teamAssigned
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      errors {
        errorType
        message
        __typename
      }
      __typename
    }
  }
`;
export const leaveTeamCustom = /* GraphQL */ `
  mutation LeaveTeamCustom($lmTeamPlayerId: ID!) {
    leaveTeamCustom(lmTeamPlayerId: $lmTeamPlayerId) {
      status
      errors {
        errorType
        message
        __typename
      }
      id
      __typename
    }
  }
`;
