/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const onCreateLmTeamInviteCodes = /* GraphQL */ `
  subscription OnCreateLmTeamInviteCodes(
    $filter: ModelSubscriptionLmTeamInviteCodesFilterInput
    $owner: String
  ) {
    onCreateLmTeamInviteCodes(filter: $filter, owner: $owner) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmTeamInviteCodes = /* GraphQL */ `
  subscription OnUpdateLmTeamInviteCodes(
    $filter: ModelSubscriptionLmTeamInviteCodesFilterInput
    $owner: String
  ) {
    onUpdateLmTeamInviteCodes(filter: $filter, owner: $owner) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmTeamInviteCodes = /* GraphQL */ `
  subscription OnDeleteLmTeamInviteCodes(
    $filter: ModelSubscriptionLmTeamInviteCodesFilterInput
    $owner: String
  ) {
    onDeleteLmTeamInviteCodes(filter: $filter, owner: $owner) {
      id
      link
      owner
      sport
      type
      email
      lmPlayerName
      lmTeamId
      expired_at
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmTeamPlayers = /* GraphQL */ `
  subscription OnCreateLmTeamPlayers(
    $filter: ModelSubscriptionLmTeamPlayersFilterInput
    $owner: String
  ) {
    onCreateLmTeamPlayers(filter: $filter, owner: $owner) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmTeamPlayers = /* GraphQL */ `
  subscription OnUpdateLmTeamPlayers(
    $filter: ModelSubscriptionLmTeamPlayersFilterInput
    $owner: String
  ) {
    onUpdateLmTeamPlayers(filter: $filter, owner: $owner) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmTeamPlayers = /* GraphQL */ `
  subscription OnDeleteLmTeamPlayers(
    $filter: ModelSubscriptionLmTeamPlayersFilterInput
    $owner: String
  ) {
    onDeleteLmTeamPlayers(filter: $filter, owner: $owner) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmTeamCoaches = /* GraphQL */ `
  subscription OnCreateLmTeamCoaches(
    $filter: ModelSubscriptionLmTeamCoachesFilterInput
    $owner: String
  ) {
    onCreateLmTeamCoaches(filter: $filter, owner: $owner) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmTeamCoaches = /* GraphQL */ `
  subscription OnUpdateLmTeamCoaches(
    $filter: ModelSubscriptionLmTeamCoachesFilterInput
    $owner: String
  ) {
    onUpdateLmTeamCoaches(filter: $filter, owner: $owner) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmTeamCoaches = /* GraphQL */ `
  subscription OnDeleteLmTeamCoaches(
    $filter: ModelSubscriptionLmTeamCoachesFilterInput
    $owner: String
  ) {
    onDeleteLmTeamCoaches(filter: $filter, owner: $owner) {
      id
      owner
      lmTeamId
      lmCoachId
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmTeam = /* GraphQL */ `
  subscription OnCreateLmTeam(
    $filter: ModelSubscriptionLmTeamFilterInput
    $owner: String
  ) {
    onCreateLmTeam(filter: $filter, owner: $owner) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmTeam = /* GraphQL */ `
  subscription OnUpdateLmTeam(
    $filter: ModelSubscriptionLmTeamFilterInput
    $owner: String
  ) {
    onUpdateLmTeam(filter: $filter, owner: $owner) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmTeam = /* GraphQL */ `
  subscription OnDeleteLmTeam(
    $filter: ModelSubscriptionLmTeamFilterInput
    $owner: String
  ) {
    onDeleteLmTeam(filter: $filter, owner: $owner) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players {
        nextToken
        startedAt
        __typename
      }
      coaches {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmCoachPlayers = /* GraphQL */ `
  subscription OnCreateLmCoachPlayers(
    $filter: ModelSubscriptionLmCoachPlayersFilterInput
    $owner: String
  ) {
    onCreateLmCoachPlayers(filter: $filter, owner: $owner) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmCoachPlayers = /* GraphQL */ `
  subscription OnUpdateLmCoachPlayers(
    $filter: ModelSubscriptionLmCoachPlayersFilterInput
    $owner: String
  ) {
    onUpdateLmCoachPlayers(filter: $filter, owner: $owner) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmCoachPlayers = /* GraphQL */ `
  subscription OnDeleteLmCoachPlayers(
    $filter: ModelSubscriptionLmCoachPlayersFilterInput
    $owner: String
  ) {
    onDeleteLmCoachPlayers(filter: $filter, owner: $owner) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmUser = /* GraphQL */ `
  subscription OnCreateLmUser(
    $filter: ModelSubscriptionLmUserFilterInput
    $owner: String
  ) {
    onCreateLmUser(filter: $filter, owner: $owner) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onUpdateLmUser = /* GraphQL */ `
  subscription OnUpdateLmUser(
    $filter: ModelSubscriptionLmUserFilterInput
    $owner: String
  ) {
    onUpdateLmUser(filter: $filter, owner: $owner) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onDeleteLmUser = /* GraphQL */ `
  subscription OnDeleteLmUser(
    $filter: ModelSubscriptionLmUserFilterInput
    $owner: String
  ) {
    onDeleteLmUser(filter: $filter, owner: $owner) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      coachedTeams {
        nextToken
        startedAt
        __typename
      }
      playerTeams {
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onCreateLmProfile = /* GraphQL */ `
  subscription OnCreateLmProfile(
    $filter: ModelSubscriptionLmProfileFilterInput
    $owner: String
  ) {
    onCreateLmProfile(filter: $filter, owner: $owner) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onUpdateLmProfile = /* GraphQL */ `
  subscription OnUpdateLmProfile(
    $filter: ModelSubscriptionLmProfileFilterInput
    $owner: String
  ) {
    onUpdateLmProfile(filter: $filter, owner: $owner) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onDeleteLmProfile = /* GraphQL */ `
  subscription OnDeleteLmProfile(
    $filter: ModelSubscriptionLmProfileFilterInput
    $owner: String
  ) {
    onDeleteLmProfile(filter: $filter, owner: $owner) {
      id
      userId
      setupComplete
      showDevicePlacement
      videoCapture
      normalized
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalizedOutdoorBallType
      distanceUnits
      speedUnits
      apexUnits
      elevationUnits
      temperatureUnits
      deviceDisplayMode
      language
      displayMode
      theme
      chosenPanel
      recordClubs
      useTileRangeView
      targetOption
      cameraView
      ballPath
      appTiles
      watchTiles
      deviceTiles
      appTilesEnabled
      watchTilesEnabled
      baseballAppData
      baseballAppTilesEnabled
      baseballDeviceTiles
      baseballWatchTiles
      clubSpeedAudio
      ballSpeedAudio
      smashFactorAudio
      attackAngleAudio
      clubPathAudio
      launchAngleAudio
      horizontalLaunchAngleAudio
      faceAngleAudio
      spinRateAudio
      spinAxisAudio
      carryDistanceAudio
      totalDistanceAudio
      sideAudio
      sideTotalAudio
      apexAudio
      features {
        nextToken
        startedAt
        __typename
      }
      devices {
        nextToken
        startedAt
        __typename
      }
      sessions {
        nextToken
        startedAt
        __typename
      }
      dataSessions {
        nextToken
        startedAt
        __typename
      }
      clubs {
        nextToken
        startedAt
        __typename
      }
      drills {
        nextToken
        startedAt
        __typename
      }
      user {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const onCreateLmFeature = /* GraphQL */ `
  subscription OnCreateLmFeature(
    $filter: ModelSubscriptionLmFeatureFilterInput
    $owner: String
  ) {
    onCreateLmFeature(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmFeature = /* GraphQL */ `
  subscription OnUpdateLmFeature(
    $filter: ModelSubscriptionLmFeatureFilterInput
    $owner: String
  ) {
    onUpdateLmFeature(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmFeature = /* GraphQL */ `
  subscription OnDeleteLmFeature(
    $filter: ModelSubscriptionLmFeatureFilterInput
    $owner: String
  ) {
    onDeleteLmFeature(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmDevice = /* GraphQL */ `
  subscription OnCreateLmDevice(
    $filter: ModelSubscriptionLmDeviceFilterInput
    $owner: String
  ) {
    onCreateLmDevice(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmDevice = /* GraphQL */ `
  subscription OnUpdateLmDevice(
    $filter: ModelSubscriptionLmDeviceFilterInput
    $owner: String
  ) {
    onUpdateLmDevice(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmDevice = /* GraphQL */ `
  subscription OnDeleteLmDevice(
    $filter: ModelSubscriptionLmDeviceFilterInput
    $owner: String
  ) {
    onDeleteLmDevice(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      advertisementName
      connectID
      configuredWifiSSID
      serialNumber
      modelNumber
      firmwareVersion
      autoConnect
      registered
      registeredUser
      registrationDate
      registrationReminder
      registrationReminderTime
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmUserStats = /* GraphQL */ `
  subscription OnCreateLmUserStats(
    $filter: ModelSubscriptionLmUserStatsFilterInput
    $owner: String
  ) {
    onCreateLmUserStats(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmUserStats = /* GraphQL */ `
  subscription OnUpdateLmUserStats(
    $filter: ModelSubscriptionLmUserStatsFilterInput
    $owner: String
  ) {
    onUpdateLmUserStats(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmUserStats = /* GraphQL */ `
  subscription OnDeleteLmUserStats(
    $filter: ModelSubscriptionLmUserStatsFilterInput
    $owner: String
  ) {
    onDeleteLmUserStats(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      sessionCount
      shotCount
      clubCount
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmSession = /* GraphQL */ `
  subscription OnCreateLmSession(
    $filter: ModelSubscriptionLmSessionFilterInput
    $owner: String
  ) {
    onCreateLmSession(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmSession = /* GraphQL */ `
  subscription OnUpdateLmSession(
    $filter: ModelSubscriptionLmSessionFilterInput
    $owner: String
  ) {
    onUpdateLmSession(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmSession = /* GraphQL */ `
  subscription OnDeleteLmSession(
    $filter: ModelSubscriptionLmSessionFilterInput
    $owner: String
  ) {
    onDeleteLmSession(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      deviceID
      startTimestamp
      endTimestamp
      duration
      name
      city
      state
      country
      address
      courseName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      normalizedElevation
      normalizedTemperature
      normalizedBallType
      normalized
      sessionQuality
      drillTargets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      shots {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmShot = /* GraphQL */ `
  subscription OnCreateLmShot(
    $filter: ModelSubscriptionLmShotFilterInput
    $owner: String
  ) {
    onCreateLmShot(filter: $filter, owner: $owner) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmShot = /* GraphQL */ `
  subscription OnUpdateLmShot(
    $filter: ModelSubscriptionLmShotFilterInput
    $owner: String
  ) {
    onUpdateLmShot(filter: $filter, owner: $owner) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmShot = /* GraphQL */ `
  subscription OnDeleteLmShot(
    $filter: ModelSubscriptionLmShotFilterInput
    $owner: String
  ) {
    onDeleteLmShot(filter: $filter, owner: $owner) {
      id
      lmSessionId
      lmDrillTargetId
      clubId
      owner
      clubCategory
      pointId
      timestamp
      isFavorite
      clubSpeed
      ballSpeed
      smashFactor
      attackAngle
      clubPath
      launchAngle
      horizontalLaunchAngle
      faceAngle
      spinRate
      spinAxis
      carryDistance
      totalDistance
      side
      sideTotal
      apex
      ballDirection
      ballCurve
      descentAngle
      dynamicLoft
      shotQuality
      targetDistance
      distanceToPin
      impactKey
      videoKey
      pointCloudKey
      protobufKey
      clubSpeedValid
      ballSpeedValid
      smashFactorValid
      attackAngleValid
      clubPathValid
      launchAngleValid
      horizontalLaunchAngleValid
      faceAngleValid
      spinRateValid
      spinAxisValid
      carryDistanceValid
      totalDistanceValid
      sideValid
      sideTotalValid
      apexValid
      descentAngleValid
      dynamicLoftValid
      xFit
      yFit
      zFit
      club {
        id
        lmProfileId
        owner
        type
        name
        brand
        model
        shaft
        color
        listOrder
        isActive
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      normalizedValues {
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        distanceToPin
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmClub = /* GraphQL */ `
  subscription OnCreateLmClub(
    $filter: ModelSubscriptionLmClubFilterInput
    $owner: String
  ) {
    onCreateLmClub(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmClub = /* GraphQL */ `
  subscription OnUpdateLmClub(
    $filter: ModelSubscriptionLmClubFilterInput
    $owner: String
  ) {
    onUpdateLmClub(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmClub = /* GraphQL */ `
  subscription OnDeleteLmClub(
    $filter: ModelSubscriptionLmClubFilterInput
    $owner: String
  ) {
    onDeleteLmClub(filter: $filter, owner: $owner) {
      id
      lmProfileId
      owner
      type
      name
      brand
      model
      shaft
      color
      listOrder
      isActive
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmDrillTemplate = /* GraphQL */ `
  subscription OnCreateLmDrillTemplate(
    $filter: ModelSubscriptionLmDrillTemplateFilterInput
  ) {
    onCreateLmDrillTemplate(filter: $filter) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmDrillTemplate = /* GraphQL */ `
  subscription OnUpdateLmDrillTemplate(
    $filter: ModelSubscriptionLmDrillTemplateFilterInput
  ) {
    onUpdateLmDrillTemplate(filter: $filter) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmDrillTemplate = /* GraphQL */ `
  subscription OnDeleteLmDrillTemplate(
    $filter: ModelSubscriptionLmDrillTemplateFilterInput
  ) {
    onDeleteLmDrillTemplate(filter: $filter) {
      id
      type
      name
      ownerName
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmDrill = /* GraphQL */ `
  subscription OnCreateLmDrill(
    $filter: ModelSubscriptionLmDrillFilterInput
    $owner: String
  ) {
    onCreateLmDrill(filter: $filter, owner: $owner) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmDrill = /* GraphQL */ `
  subscription OnUpdateLmDrill(
    $filter: ModelSubscriptionLmDrillFilterInput
    $owner: String
  ) {
    onUpdateLmDrill(filter: $filter, owner: $owner) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmDrill = /* GraphQL */ `
  subscription OnDeleteLmDrill(
    $filter: ModelSubscriptionLmDrillFilterInput
    $owner: String
  ) {
    onDeleteLmDrill(filter: $filter, owner: $owner) {
      id
      owner
      lmProfileId
      templateId
      type
      name
      logoUrl
      targets {
        id
        type
        targetQuality
        shotCount
        shotType
        clubCategory
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmSessionStats = /* GraphQL */ `
  subscription OnCreateLmSessionStats(
    $filter: ModelSubscriptionLmSessionStatsFilterInput
    $owner: String
  ) {
    onCreateLmSessionStats(filter: $filter, owner: $owner) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmSessionStats = /* GraphQL */ `
  subscription OnUpdateLmSessionStats(
    $filter: ModelSubscriptionLmSessionStatsFilterInput
    $owner: String
  ) {
    onUpdateLmSessionStats(filter: $filter, owner: $owner) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmSessionStats = /* GraphQL */ `
  subscription OnDeleteLmSessionStats(
    $filter: ModelSubscriptionLmSessionStatsFilterInput
    $owner: String
  ) {
    onDeleteLmSessionStats(filter: $filter, owner: $owner) {
      id
      lmSessionId
      owner
      shotCount
      values {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        shotQuality
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      averages {
        clubId
        clubSpeed
        ballSpeed
        smashFactor
        attackAngle
        clubPath
        launchAngle
        horizontalLaunchAngle
        faceAngle
        spinRate
        spinAxis
        carryDistance
        totalDistance
        side
        sideTotal
        apex
        ballDirection
        ballCurve
        descentAngle
        dynamicLoft
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmShareSession = /* GraphQL */ `
  subscription OnCreateLmShareSession(
    $filter: ModelSubscriptionLmShareSessionFilterInput
    $owner: String
  ) {
    onCreateLmShareSession(filter: $filter, owner: $owner) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmShareSession = /* GraphQL */ `
  subscription OnUpdateLmShareSession(
    $filter: ModelSubscriptionLmShareSessionFilterInput
    $owner: String
  ) {
    onUpdateLmShareSession(filter: $filter, owner: $owner) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmShareSession = /* GraphQL */ `
  subscription OnDeleteLmShareSession(
    $filter: ModelSubscriptionLmShareSessionFilterInput
    $owner: String
  ) {
    onDeleteLmShareSession(filter: $filter, owner: $owner) {
      shareUrl
      lmSessionId
      owner
      shareVideo
      expiresAt
      session {
        startTimestamp
        endTimestamp
        duration
        name
        address
        elevation
        temperature
        humidity
        location
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        sessionQuality
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmDataResults = /* GraphQL */ `
  subscription OnCreateLmDataResults(
    $filter: ModelSubscriptionLmDataResultsFilterInput
    $owner: String
  ) {
    onCreateLmDataResults(filter: $filter, owner: $owner) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmDataResults = /* GraphQL */ `
  subscription OnUpdateLmDataResults(
    $filter: ModelSubscriptionLmDataResultsFilterInput
    $owner: String
  ) {
    onUpdateLmDataResults(filter: $filter, owner: $owner) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmDataResults = /* GraphQL */ `
  subscription OnDeleteLmDataResults(
    $filter: ModelSubscriptionLmDataResultsFilterInput
    $owner: String
  ) {
    onDeleteLmDataResults(filter: $filter, owner: $owner) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      baseballResults {
        pitcher
        hitter
        pitcherName
        hitterName
        __typename
      }
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onCreateLmDataSession = /* GraphQL */ `
  subscription OnCreateLmDataSession(
    $filter: ModelSubscriptionLmDataSessionFilterInput
    $owner: String
  ) {
    onCreateLmDataSession(filter: $filter, owner: $owner) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onUpdateLmDataSession = /* GraphQL */ `
  subscription OnUpdateLmDataSession(
    $filter: ModelSubscriptionLmDataSessionFilterInput
    $owner: String
  ) {
    onUpdateLmDataSession(filter: $filter, owner: $owner) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const onDeleteLmDataSession = /* GraphQL */ `
  subscription OnDeleteLmDataSession(
    $filter: ModelSubscriptionLmDataSessionFilterInput
    $owner: String
  ) {
    onDeleteLmDataSession(filter: $filter, owner: $owner) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      results {
        nextToken
        startedAt
        __typename
      }
      profile {
        id
        userId
        setupComplete
        showDevicePlacement
        videoCapture
        normalized
        normalizedElevation
        normalizedTemperature
        normalizedBallType
        normalizedOutdoorBallType
        distanceUnits
        speedUnits
        apexUnits
        elevationUnits
        temperatureUnits
        deviceDisplayMode
        language
        displayMode
        theme
        chosenPanel
        recordClubs
        useTileRangeView
        targetOption
        cameraView
        ballPath
        appTiles
        watchTiles
        deviceTiles
        appTilesEnabled
        watchTilesEnabled
        baseballAppData
        baseballAppTilesEnabled
        baseballDeviceTiles
        baseballWatchTiles
        clubSpeedAudio
        ballSpeedAudio
        smashFactorAudio
        attackAngleAudio
        clubPathAudio
        launchAngleAudio
        horizontalLaunchAngleAudio
        faceAngleAudio
        spinRateAudio
        spinAxisAudio
        carryDistanceAudio
        totalDistanceAudio
        sideAudio
        sideTotalAudio
        apexAudio
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      team {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
