"""
User information is stored in the database to facilitate offline user data.

NOTE: Must be synced with Authorization remote when possible
"""
type LmUser @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: private, provider: iam, operations: [update, create, get] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  setupComplete: Boolean!

  email: String
  phone: String
  fullName: String
  profileImage: String
  userType: String
  gender: String
  handedness: LmHandedness
  birthdate: Float

  # Registration Information
  companyName: String
  shippingAddressLine1: String
  shippingAddressLine2: String
  shippingPostcode: String
  shippingLocality: String
  shippingRegion: String
  shippingCountry: String
  subscriptions: String

  #Base ball
  competitiveLevel: CompetitiveLevel
  teamName: String
  organizationSchool: String
  baseballPlayerPosition: String

  coachedTeams: [LmTeamCoaches] @hasMany(indexName: "byCoach", fields: ["id"]) #@Multisport
  playerTeams: [LmTeamPlayers] @hasMany(indexName: "byPlayer", fields: ["id"]) #@Multisport
}

enum CompetitiveLevel {
    YouthLessThan13
    HighSchool
    College
    Professional
    Amateur
}

"""
Profile is accessible by searching by userId
"""
type LmProfile @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: private, provider: iam, operations: [update, create] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  userId: ID! @index(name: "byUser", queryField: "profileByOwner")

  setupComplete: Boolean!
  showDevicePlacement: Boolean
  videoCapture: Boolean

  # Golf
  normalized: Boolean
  normalizedElevation: Int
  normalizedTemperature: Int
  normalizedBallType: LmBallType
  normalizedOutdoorBallType: LmBallType

  # Generic LM settings
  distanceUnits: LmDistanceUnits
  speedUnits: LmSpeedUnits
  apexUnits: LmDistanceUnits
  elevationUnits: LmDistanceUnits
  temperatureUnits: LmTemperatureUnits
  deviceDisplayMode: LmDisplayMode

  # App settings
  language: String
  displayMode: LmDisplayMode
  theme: LmTheme

  # Deprecated (Unity)
  chosenPanel: Int
  recordClubs: Boolean
  useTileRangeView: Boolean
  targetOption: LmTargetOption @deprecated(reason: "Not supported.")
  cameraView: LmCameraView @deprecated(reason: "Not supported.")
  ballPath: LmBallPath @deprecated(reason: "Not supported.")

  # Golf data
  appTiles: [ValueType]
  watchTiles: [ValueType]
  deviceTiles: [ValueType]
  appTilesEnabled: [Boolean]
  watchTilesEnabled: [Boolean]

  # Baseball data
  baseballAppData: [BaseBallValueType]
  baseballAppTilesEnabled: [Boolean]
  baseballDeviceTiles: [BaseBallValueType]
  baseballWatchTiles: [BaseBallValueType]

  # Golf audio
  clubSpeedAudio: Boolean
  ballSpeedAudio: Boolean
  smashFactorAudio: Boolean
  attackAngleAudio: Boolean
  clubPathAudio: Boolean
  launchAngleAudio: Boolean
  horizontalLaunchAngleAudio: Boolean
  faceAngleAudio: Boolean
  spinRateAudio: Boolean
  spinAxisAudio: Boolean
  carryDistanceAudio: Boolean
  totalDistanceAudio: Boolean
  sideAudio: Boolean
  sideTotalAudio: Boolean
  apexAudio: Boolean

  # Relational Models
  features: [LmFeature] @hasMany(indexName: "byProfile", fields: ["id"])
  devices: [LmDevice] @hasMany(indexName: "byProfile", fields: ["id"])
  sessions: [LmSession] @hasMany(indexName: "byProfile", fields: ["id"]) #@Golf
  dataSessions: [LmDataSession] @hasMany(indexName: "byProfile", fields: ["id"]) #@Multisport
  clubs: [LmClub] @hasMany(indexName: "byProfile", fields: ["id"]) #@Golf
  drills: [LmDrill] @hasMany(indexName: "byProfile", fields: ["id"]) #@Golf
  user: LmUser @hasOne(fields: ["userId"])
}

enum LmDistanceUnits {
  Feet
  Yards
  Meters
}

enum LmSpeedUnits {
  Mph
  Kph
  Mps
}

enum LmTemperatureUnits {
  Fahrenheit
  Celcius
}

enum LmDisplayMode {
  Graphic
  Tiles
  MultiData
  SingleData
}

enum LmTheme {
  Light
  Dark
}

enum LmTargetOption {
  None
  Basket
  Green
  Circle
}

enum LmCameraView {
  Stationary
  Ball
  Flight
}

enum LmBallPath {
  Simulated
  Raw
}

enum LmBallType {
  Premium
  TitleistRct
}

enum LmHandedness {
  Left
  Right
}

enum DataType {
    Int
    Float
}

enum LmLocation {
  Net
  Screen
  Simulator
  OutdoorRange
  IndoorRange
  Course
  Field
}

enum ValueType {
  ClubSpeed
  BallSpeed
  SmashFactor
  AttackAngle
  ClubPath
  LaunchAngle
  HorizontalLaunchAngle
  FaceAngle
  FaceToPath
  SpinRate
  SpinAxis
  CarryDistance
  TotalDistance
  Side
  SideTotal
  Apex
  BallDirection
  BallCurve
  DescentAngle
}
enum BaseBallValueType {
  PitchSpin
  PitchSpeed
  ExitVelocity
  LaunchAngle
  HorizontalLaunchAngle
  Distance
  Apex
  BatSpeed
}

enum BaseballValueType {
  pitchReleaseTimestamp
  pitchReleaseWorld3dPositionMeters
  pitchReleaseRadar3dPositionMetersDeg
  pitchReleaseVelocityMps
  pitchReleaseArmSlotDeg
  pitchReleaseHorizontalAngleDeg
  pitchReleaseVerticalAngleDeg
  pitchReleaseBackwardExtensionMeters

  pitchBreakHorizontalMeters
  pitchBreakVerticalMeters
  pitchBreakInducedVerticalMeters

  pitchSpinTotalRpm
  pitchSpinActiveRpm
  pitchSpinBackRpm
  pitchSpinSideRpm
  pitchSpinTopRpm
  pitchSpinDirectionClockBearing
  pitchSpinDirectionEfficiencyPct

  pitchApproachVelocityMps
  pitchApproachPlateTimestamp
  pitchApproachPlateWorld3dPositionMeters

  pitchCrossPlateTimestamp
  pitchCrossPlateWorld3dPositionMeters

  hitDirectionDeg
  hitDistanceMeters
  hitExitVelocityMps
  hitLaunchAngleDeg

  hitSpinTotalRpm
  hitSpinActiveRpm
  hitSpinBackRpm
  hitSpinSideRpm
  hitSpinTopRpm
  hitSpinDirectionClockBearing
  hitSpinDirectionEfficiencyPct

  hitBallContactPresence
  hitBallContactTimestamp
  hitBallContactWorld3dPositionMeters
  hitBallContactRadar3dPositionMetersDeg

  batSpeedMps
  batRotationalAccelerationGs
  batAttackAngleDeg
  batEarlyConnectionDeg
  batConnectionAtImpactDeg
  batVerticalAngleDeg
  batSwingStartPresence
  batSwingStartTimestamp
  batPeakHandSpeedMps

  teedBallLocationWorld3dPositionMeters
}

enum LmGolfTrajectoryType {
    Flight
    Normalized
}

enum LmBaseballTrajectoryType {
    Unknown
    Pitch
    Hit
}

enum LmUserRole {
    Users
    Coaches
    Admin
}

enum LmTeamPlayerType {
    Batting
    Pitching
    Both
}

enum LmDataSessionPlayMode {
  LiveAtBat
  BattingPract
  TeeMode
}

type LmClockBearing {
  hours: Int
  minutes: Int
}

type LmTimestamp {
  seconds: Int
  nanos: Int
}

type Lm2dPosition {
  x_pos: Float
  y_pos: Float
}

type Lm3dPosition {
  x_pos: Float
  y_pos: Float
  z_pos: Float
}

type LmSphericalPoint3d {
  r: Float
  theta: Float
  phi: Float
}

type LmOrientation3d {
  pitch: Float
  yaw: Float
  roll: Float
}

type LmPolynomial {
  x_coefficients: [Float]
  y_coefficients: [Float]
  z_coefficients: [Float]
}

type LmTrajectory {
  baseballType: LmBaseballTrajectoryType
  golfType: LmGolfTrajectoryType
  polynomial: LmPolynomial
}


"""
LM App Features Access
"""
type LmFeature @model
  @auth(rules: [
    { allow: owner, identityClaim: "username", operations: [read] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmProfileId: ID!
    @index(name: "byProfile", queryField: "featuresByProfile")
  owner: String @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
    @index(name: "byUser", queryField: "featuresByUser")

  featureName: String
  enabled: Boolean @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
  expiresAt: AWSDateTime
}

"""
LM Device Identity
"""
type LmDevice @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmProfileId: ID!
    @index(name: "byProfile", queryField: "devicesByProfile")
  owner: String
    @index(name: "byUser", queryField: "devicesByUser")

  deviceID: String
  advertisementName: String
  connectID: String @deprecated(reason: "Not supported.")
  configuredWifiSSID: String
  serialNumber: String
  modelNumber: String
  firmwareVersion: String
  autoConnect: Boolean @deprecated(reason: "Not supported.")

  # Device registration
  registered: Boolean
  registeredUser: String
  registrationDate: Int
  registrationReminder: Boolean
  registrationReminderTime: Int
}

